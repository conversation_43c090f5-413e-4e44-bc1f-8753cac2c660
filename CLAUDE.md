# Claude Code Memory

## Form Inputs Organization Pattern

### Standard Location
All reusable form input components should be placed in `/src/components/form-inputs/` directory.

### Component Structure
- All form inputs should use react-hook-form integration
- Follow the pattern: `FormXxxInput` naming convention
- Use FormDescription/FormMessage pattern with conditional rendering
- Hide FormDescription when there's an error, show FormMessage instead

### Card-related Components
Card input components have been organized and moved to form-inputs:
- `FormCardInput` - Card number input with live type detection
- `FormCVVInput` - CVV input with card-specific validation and show/hide toggle
- `FormExpiryInput` - Expiry date input with MM/YY formatting and validation

### Export Pattern
Always add new form inputs to `/src/components/form-inputs/index.ts` for easy importing.

### Usage
Components can be imported together:
```typescript
import { FormInput, FormCardInput, FormCVVInput, FormExpiryInput } from '@/components/form-inputs'
```

### Why This Pattern
- Reusability across different modules
- Consistent react-hook-form integration
- Better organization and maintainability
- Shared validation and styling patterns

## TypeScript Error Checking

### Always Check for TS Errors
After any code changes, ALWAYS run TypeScript diagnostics to ensure no compilation errors:
- Use `mcp__ide__getDiagnostics` to check for TypeScript errors
- Fix any critical errors (Error severity) before completing tasks
- Address warnings when possible
- Spelling errors (cSpell) are informational only

### Current Status
- ✅ No critical TypeScript errors found
- ⚠️ Minor warnings in form-address-input.tsx (unused variables)
- ⚠️ Minor warning in form-card-input.tsx (any type usage)
- ℹ️ Spelling warnings for financial terms (SEPA, IBAN, etc.) - can be ignored