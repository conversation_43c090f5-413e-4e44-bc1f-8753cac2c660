'use server'

import { manageClientDataManagementToken } from '@/modules/auth/actions/auth-actions'
import { revalidateTag } from 'next/cache'
import { z } from 'zod'

import { fetchAuthenticatedApi } from '@/lib/fetch-with-auth-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { DATA_MANAGEMENT_TAGS } from './data-management-tags'

// Schemas
const dataManagementStepSchema = z.object({
  title: z.string(),
  isActive: z.boolean(),
  isLast: z.boolean(),
  isCompleted: z.boolean(),
})

const navigationStateSchema = z.object({
  currentStep: z.string(),
  navigationSteps: z.array(dataManagementStepSchema),
})

const clientDataStepsSchema = z.record(z.any()).optional()

const saveDataManagementSchema = z.object({
  formData: z.object({
    steps: clientDataStepsSchema,
    navigationState: navigationStateSchema,
  }),
  status: z.enum(['DRAFT', 'DONE']).optional(),
})

// Server Actions
export const saveDataManagementAction = authenticatedAction
  .schema(saveDataManagementSchema)
  .action(async ({ parsedInput: { formData, status }, ctx: { session } }) => {
    // Get user ID from session
    const userId = session?.user?.id
    if (!userId) {
      throw new Error('User ID not found in session')
    }

    // Use form data directly without transformation
    const directFormData = {
      steps: formData.steps || {},
      navigationState: formData.navigationState,
    }

    const clientDataManagementToken = await manageClientDataManagementToken({
      action: 'get',
    })

    // Prepare request body - only include status if it's "DONE"
    const requestBody: Record<string, any> = {
      formData: directFormData,
    }

    // Only include status field when it's explicitly "DONE" (final step completion)
    if (status === 'DONE') {
      requestBody.status = status
    }

    // Use the new backend API endpoint with authentication
    const request = await fetchAuthenticatedApi(`/client_data_managements/${clientDataManagementToken}`, {
      method: 'PUT',
      logCurl: true,
      newApi: true,
      body: JSON.stringify(requestBody),
    })

    if (request.error) {
      console.error('❌ [saveDataManagementAction] API request failed:', request.error)
      return {
        serverError: 'Failed to save data management form',
      }
    }

    revalidateTag(DATA_MANAGEMENT_TAGS.ALL)

    console.log('✅ [saveDataManagementAction] Save successful:', request.data)

    return request.data
  })

export const resetDataManagementAction = authenticatedAction.action(async ({ ctx: { session } }) => {
  // Get user ID from session
  const clientDataManagementToken = await manageClientDataManagementToken({
    action: 'get',
  })

  // put form data to be null
  const request = await fetchAuthenticatedApi(`/client_data_managements/${clientDataManagementToken}`, {
    method: 'PUT',
    newApi: true,
    body: JSON.stringify({ formData: null }),
  })

  if (request.error) {
    console.error('❌ [resetDataManagementAction] API request failed:', request.error)
    return {
      serverError: 'Failed to reset data management form',
    }
  }

  revalidateTag(DATA_MANAGEMENT_TAGS.ALL)

  return request.data
})
