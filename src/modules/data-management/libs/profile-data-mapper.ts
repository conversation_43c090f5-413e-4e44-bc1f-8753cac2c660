import { ProfileFormInputs } from '@/modules/profile/types/profile-schema'

import { ClientDataSteps } from '../types/data-management-types'

/**
 * Maps data management form data to profile API format
 * Combines clientInformation, defaultAddress, and contactData steps into a single profile object
 */
export function mapDataManagementToProfile(formData: ClientDataSteps): Partial<ProfileFormInputs> {
  const profileData: Partial<ProfileFormInputs> = {}

  // Map client information fields
  if (formData.clientInformation) {
    const clientInfo = formData.clientInformation
    profileData.salutation = clientInfo.salutation
    profileData.title = clientInfo.title
    profileData.firstName = clientInfo.firstName
    profileData.lastName = clientInfo.lastName
    profileData.familyStatus = clientInfo.familyStatus
    profileData.birthName = clientInfo.birthName
    profileData.birthdate = clientInfo.birthdate
    profileData.birthPlace = clientInfo.birthPlace
    profileData.birthCountry = clientInfo.birthCountry
    profileData.nationality = clientInfo.nationality
  }

  // Map legitimation fields
  if (formData.legitimation) {
    const legitimation = formData.legitimation
    profileData.idCardNumber = legitimation.idCardNumber
    profileData.idCardDate = legitimation.idCardDate
    profileData.idCardAuthority = legitimation.idCardAuthority
  }

  // Map default address fields
  if (formData.defaultAddress) {
    const address = formData.defaultAddress
    profileData.street = address.street
    profileData.streetNum = address.streetNum
    profileData.zip = address.zip
    profileData.city = address.city
    profileData.country = address.country
    profileData.district = address.district
  }

  // Map contact data fields
  if (formData.contactData) {
    const contactData = formData.contactData
    profileData.email = contactData.email
    profileData.preferredContactType = contactData.preferredContactType
    profileData.preferredAppeal = contactData.preferredAppeal
    profileData.phone = contactData.phone
    profileData.mobile = contactData.mobile
    profileData.businessPhone = contactData.businessPhone
  }

  return profileData
}

/**
 * Extracts only the profile-related steps from data management form data
 * Returns the steps that should be validated against the profile API
 */
export function extractProfileSteps(formData: ClientDataSteps): ClientDataSteps {
  const profileSteps: ClientDataSteps = {}

  // Only include steps that map to profile fields
  if (formData.clientInformation) {
    profileSteps.clientInformation = formData.clientInformation
  }

  if (formData.legitimation) {
    profileSteps.legitimation = formData.legitimation
  }

  if (formData.defaultAddress) {
    profileSteps.defaultAddress = formData.defaultAddress
  }

  if (formData.contactData) {
    profileSteps.contactData = formData.contactData
  }

  return profileSteps
}

/**
 * Checks if the current step data contains profile-related information
 * Used to determine if profile validation is needed before saving
 */
export function isProfileRelatedStep(stepName: string): boolean {
  const profileSteps = ['clientInformation', 'legitimation', 'defaultAddress', 'contactData']
  return profileSteps.includes(stepName)
}

/**
 * Validates that required profile fields are present
 * Returns validation errors if required fields are missing
 */
export function validateProfileData(profileData: Partial<ProfileFormInputs>): {
  isValid: boolean
  errors: Record<string, string[]>
} {
  const errors: Record<string, string[]> = {}

  // Check required fields based on profile schema
  if (!profileData.firstName?.trim()) {
    errors.firstName = ['First name is required']
  }

  if (!profileData.lastName?.trim()) {
    errors.lastName = ['Last name is required']
  }

  if (!profileData.street?.trim()) {
    errors.street = ['Street is required']
  }

  if (!profileData.streetNum?.trim()) {
    errors.streetNum = ['Street number is required']
  }

  if (!profileData.zip?.trim()) {
    errors.zip = ['ZIP code is required']
  }

  if (!profileData.city?.trim()) {
    errors.city = ['City is required']
  }

  if (!profileData.country?.trim()) {
    errors.country = ['Country is required']
  }

  if (profileData.email && !isValidEmail(profileData.email)) {
    errors.email = ['Invalid email format']
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

/**
 * Simple email validation helper
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Maps profile validation errors back to data management step format
 * This helps display errors in the correct step context
 */
export function mapProfileErrorsToSteps(
  profileErrors: Record<string, string[]>
): Record<string, Record<string, string[]>> {
  const stepErrors: Record<string, Record<string, string[]>> = {}

  // Map errors to their respective steps
  const fieldToStepMapping: Record<string, string> = {
    // Client Information fields
    salutation: 'clientInformation',
    title: 'clientInformation',
    firstName: 'clientInformation',
    lastName: 'clientInformation',
    familyStatus: 'clientInformation',
    birthName: 'clientInformation',
    birthdate: 'clientInformation',
    birthPlace: 'clientInformation',
    birthCountry: 'clientInformation',
    nationality: 'clientInformation',

    // Legitimation fields
    idCardNumber: 'legitimation',
    idCardDate: 'legitimation',
    idCardAuthority: 'legitimation',

    // Default Address fields
    street: 'defaultAddress',
    streetNum: 'defaultAddress',
    zip: 'defaultAddress',
    city: 'defaultAddress',
    country: 'defaultAddress',
    district: 'defaultAddress',

    // Contact Data fields
    email: 'contactData',
    preferredContactType: 'contactData',
    preferredAppeal: 'contactData',
    phone: 'contactData',
    mobile: 'contactData',
    businessPhone: 'contactData',
  }

  Object.entries(profileErrors).forEach(([fieldName, errors]) => {
    const stepName = fieldToStepMapping[fieldName]
    if (stepName) {
      if (!stepErrors[stepName]) {
        stepErrors[stepName] = {}
      }
      stepErrors[stepName][fieldName] = errors
    }
  })

  return stepErrors
}
