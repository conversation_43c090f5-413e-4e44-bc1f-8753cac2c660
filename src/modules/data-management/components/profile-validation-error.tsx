'use client'

import { Al<PERSON><PERSON><PERSON><PERSON>, X } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useState } from 'react'

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface ProfileValidationErrorProps {
  error: string
  details?: Record<string, string[]>
  onDismiss?: () => void
  onRetry?: () => void
  className?: string
}

/**
 * Component for displaying profile validation errors in the data management flow
 * Shows detailed validation errors with field-specific feedback
 */
export function ProfileValidationError({
  error,
  details,
  onDismiss,
  onRetry,
  className,
}: ProfileValidationErrorProps) {
  const t = useTranslations()
  const [isExpanded, setIsExpanded] = useState(false)

  const hasDetails = details && Object.keys(details).length > 0

  return (
    <Card className={`border-destructive bg-destructive/5 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-destructive/10">
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </div>
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium text-destructive">
                {t('DATA_MANAGEMENT.PROFILE_VALIDATION_ERROR.TITLE', {
                  defaultValue: 'Profile Validation Failed',
                })}
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {error ||
                  t('DATA_MANAGEMENT.PROFILE_VALIDATION_ERROR.DEFAULT_MESSAGE', {
                    defaultValue: 'There were validation errors with your profile data.',
                  })}
              </CardDescription>
            </div>
          </div>
          {onDismiss && (
            <Button variant="ghost" size="sm" onClick={onDismiss} className="h-6 w-6 p-0">
              <X className="h-3 w-3" />
              <span className="sr-only">Dismiss</span>
            </Button>
          )}
        </div>
      </CardHeader>

      {hasDetails && (
        <CardContent className="pt-0">
          <div className="space-y-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
            >
              {isExpanded
                ? t('DATA_MANAGEMENT.PROFILE_VALIDATION_ERROR.HIDE_DETAILS', {
                    defaultValue: 'Hide details',
                  })
                : t('DATA_MANAGEMENT.PROFILE_VALIDATION_ERROR.SHOW_DETAILS', {
                    defaultValue: 'Show details',
                  })}
            </Button>

            {isExpanded && (
              <div className="space-y-2">
                {Object.entries(details).map(([field, errors]) => (
                  <Alert key={field} variant="destructive" className="py-2">
                    <AlertTitle className="text-xs font-medium">
                      {t(`DATA_MANAGEMENT.FIELDS.${field.toUpperCase()}`, {
                        defaultValue: field,
                      })}
                    </AlertTitle>
                    <AlertDescription className="text-xs">
                      {errors.map((error, index) => (
                        <div key={index}>{error}</div>
                      ))}
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            )}
          </div>

          {onRetry && (
            <div className="mt-4 flex justify-end">
              <Button variant="outline" size="sm" onClick={onRetry}>
                {t('DATA_MANAGEMENT.PROFILE_VALIDATION_ERROR.RETRY', {
                  defaultValue: 'Try Again',
                })}
              </Button>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}

/**
 * Inline profile validation error component for use within forms
 */
export function InlineProfileValidationError({
  error,
  details,
  className,
}: Omit<ProfileValidationErrorProps, 'onDismiss' | 'onRetry'>) {
  const t = useTranslations()
  const hasDetails = details && Object.keys(details).length > 0

  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle className="text-sm">
        {t('DATA_MANAGEMENT.PROFILE_VALIDATION_ERROR.INLINE_TITLE', {
          defaultValue: 'Profile validation failed',
        })}
      </AlertTitle>
      <AlertDescription className="text-sm">
        <div className="space-y-1">
          <p>
            {error ||
              t('DATA_MANAGEMENT.PROFILE_VALIDATION_ERROR.INLINE_MESSAGE', {
                defaultValue: 'Please correct the following errors before proceeding.',
              })}
          </p>
          {hasDetails && (
            <div className="mt-2 space-y-1">
              {Object.entries(details).map(([field, errors]) => (
                <div key={field} className="text-xs">
                  <span className="font-medium">
                    {t(`DATA_MANAGEMENT.FIELDS.${field.toUpperCase()}`, {
                      defaultValue: field,
                    })}
                    :
                  </span>{' '}
                  {errors.join(', ')}
                </div>
              ))}
            </div>
          )}
        </div>
      </AlertDescription>
    </Alert>
  )
}

/**
 * Toast-style profile validation error for use with Sonner
 */
export function createProfileValidationErrorToast(error: string, details?: Record<string, string[]>) {
  const hasDetails = details && Object.keys(details).length > 0
  
  let description = error
  
  if (hasDetails) {
    const fieldErrors = Object.entries(details)
      .map(([field, errors]) => `${field}: ${errors.join(', ')}`)
      .join('; ')
    description = `${error}. Details: ${fieldErrors}`
  }
  
  return {
    title: 'Profile Validation Failed',
    description,
    duration: 8000, // Longer duration for validation errors
  }
}
