'use client'

import { useTranslations } from 'next-intl'

import { InlineProfileValidationError, ProfileValidationError } from './profile-validation-error'

interface DataManagementErrorDisplayProps {
  profileValidationError?: {
    error: string
    details?: Record<string, string[]>
  } | null
  onDismissProfileError?: () => void
  onRetryProfileValidation?: () => void
  variant?: 'card' | 'inline'
  className?: string
}

/**
 * Component for displaying various error types in the data management flow
 * Handles profile validation errors and other data management specific errors
 */
export function DataManagementErrorDisplay({
  profileValidationError,
  onDismissProfileError,
  onRetryProfileValidation,
  variant = 'card',
  className,
}: DataManagementErrorDisplayProps) {
  const t = useTranslations()

  // Don't render if no errors
  if (!profileValidationError) {
    return null
  }

  // Render profile validation error
  if (profileValidationError) {
    if (variant === 'inline') {
      return (
        <InlineProfileValidationError
          error={profileValidationError.error}
          details={profileValidationError.details}
          className={className}
        />
      )
    }

    return (
      <ProfileValidationError
        error={profileValidationError.error}
        details={profileValidationError.details}
        onDismiss={onDismissProfileError}
        onRetry={onRetryProfileValidation}
        className={className}
      />
    )
  }

  return null
}

/**
 * Hook to provide error display props from data management hook
 */
export function useDataManagementErrorDisplay(dataManagementHook: {
  profileValidationError?: {
    error: string
    details?: Record<string, string[]>
  } | null
  clearProfileValidationError?: () => void
}) {
  const { profileValidationError, clearProfileValidationError } = dataManagementHook

  return {
    profileValidationError,
    onDismissProfileError: clearProfileValidationError,
    onRetryProfileValidation: () => {
      // Clear the error and let the user try again
      clearProfileValidationError?.()
    },
  }
}
